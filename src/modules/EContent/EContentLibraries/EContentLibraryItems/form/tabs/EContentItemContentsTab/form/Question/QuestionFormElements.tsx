import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { get, isEmpty, isNumber } from 'lodash';
import classNames from 'classnames';

import useT from '../../../../../../../../../common/components/utils/Translations/useT';
import TextEditorField from '../../../../../../../../../common/components/containers/EntityForm/fields/TextEditorField';
import CheckboxField from '../../../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import QuestionSetOption from './QuestionSetOption';
import { IMAGES } from '../../../../../../../../../common/propTypes';
import { E_CONTENT_CONTENT_FILE } from '../../../../../../../../../fsCategories';
import useCurrentUser from '../../../../../../../../../common/data/hooks/useCurrentUser';
import SelectBoxField from '../../../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import AttachmentItemCard from '../../../../../../../../EdCom/common/AttachmentItemCard';
import AttachmentFieldWithPreview from '../../../../../../../../../common/components/containers/EntityForm/fields/AttachmentFieldWithPreview';
import TagInputField from '../../../../../../../../../common/components/containers/EntityForm/fields/TagInputField';
import {
  SINGLE_SELECT,
  MULTI_SELECT,
  TRUEFALSE,
  MULTI_TRUEFALSE,
  NUMBER,
  NUMBER_QUESTION,
  SHORT_TEXT,
  PARAGRAPH_QUESTION,
  ESSAY_QUESTION,
} from '../../../../../../../../../model/QuestionType';
import AnimatedTitle from '../../../../../../../../../common/components/controls/base/AnimatedTitle';
import EntityForm from '../../../../../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../../../../../common/components/containers/EntityForm/EntityForm';
import styles from '../EContentItemContentForm.scss';
import IEContentQuestion from '../../../../../../../../../common/abstract/EContent/IEContentQuestion';
import TextField from '../../../../../../../../../common/components/containers/EntityForm/fields/TextField';
import {
  tenToHundredOneTwentyToTwoHundredTwoHundredFiftyToFourHundred,
  hundredToFiveHundredThousandToFiveThousandTenThousandToFiftyThousand,
} from '../../../../../../../../Organization/OrganisationGroups/EContentSetup/model/EContentAttributesOptions';
import { RETURN_TYPE } from '../../../../../../../../../common/components/controls/base/SelectBox';
import NumberField from '../../../../../../../../../common/components/containers/EntityForm/fields/NumberField';
import TextAreaField from '../../../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import Spinner from '../../../../../../../../../common/components/utils/Spinner';
import RoundedLinkButton from '../../../../../../../../../common/components/controls/RoundedLinkButton';
import RoundedGoBackButton from '../../../../../../../../../common/components/controls/RoundedGoBackButton';
import RoundedDropdownButton from '../../../../../../../../../common/components/controls/RoundedDropdownButton';
import RoundedDangerButton from '../../../../../../../../../common/components/controls/RoundedDangerButton';
import swal2 from '../../../../../../../../../common/utils/swal2';

export interface IQuestionFormElements {
  isKeywordRequired?: boolean;
  maxTagLength?: number;
  hasKeyword?: boolean;
  suggestions?: string[];
  isReadOnly?: boolean;
  onCancel: () => void;
  entity: IEContentQuestion;
  onDelete: (index: number) => Promise<boolean>;
  onSubmit: (Item: any) => Promise<void>;
  onSubmitClose: (Item: any) => Promise<void>;
  addNewButtonList?: () => JSX.Element;
  quickAddComponent?: JSX.Element;
  addSubmitOption?: (isSubmitting: boolean) => JSX.Element;
  clobsPreviewData?: any;
  hasRenderedPreview?: boolean;
}
const HUNDRED = 100;
const FOUR_HUNDERED = 400;
const MAX_INT = 2000000000;
const TEN = 10;

const QuestionFormElements: React.FC<IQuestionFormElements> = ({
  isKeywordRequired,
  maxTagLength,
  hasKeyword,
  suggestions,
  isReadOnly,
  onCancel,
  entity,
  onDelete,
  onSubmit,
  onSubmitClose,
  addNewButtonList,
  quickAddComponent,
  addSubmitOption,
  clobsPreviewData,
  hasRenderedPreview,
}) => {
  const t = useT();
  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();
  const questionMultiple = get(entity, 'questionMultiple', false) || false;
  const [isMultiple, setIsMultiple] = useState(questionMultiple);
  const [correctNumber, setCorrectNumber] = useState(0);
  const [refreshPage, setRefreshPage] = useState(false);
  useEffect(() => {
    setRefreshPage(true);
    setTimeout(() => setRefreshPage(false), HUNDRED);
  }, [entity, setRefreshPage]);
  const textEditorConfig = useMemo(
    () => ({
      toolbarButtons: [
        'bold',
        'italic',
        'underline',
        'insertImage',
        'alignLeft',
        'alignCenter',
        'alignRight',
        'alignJustify',
      ],
    }),
    [],
  );

  const attachmentItemComponent = useCallback(
    props => <AttachmentItemCard hideCaption hideDetail {...props} />,
    [],
  );
  const [values, setValues] = useState(entity);
  const validate = useCallback(values => {
    setValues(values);
  }, []);
  const optionslist = useMemo(() => get(values, `options`, []), [values]);
  const hasQuestionPoint = useMemo(
    () => optionslist.find(x => isNumber(x.questionPoint)),
    [optionslist],
  );
  const questionType = useMemo(() => get(entity, `questionType`, 1), [entity]);
  const isNew = useMemo(() => !entity?.questionTypeId, [entity]);
  const customLabel = (
    <>
      <AnimatedTitle
        isRequired
        value
        className="mb-5"
        placeholder={t('Question')}
      />
      {quickAddComponent}
    </>
  );
  const handleCheck = useCallback(() => {
    setIsMultiple(true);
  }, []);
  const handleUnCheck = useCallback(() => {
    setIsMultiple(false);
  }, []);

  const _onDelete = useCallback(async () => {
    const { isConfirmed } = await swal2({
      text: t(`Are you sure you want to remove this question?`),
      icon: 'warning',
      deleteMode: true,
      confirmButtonText: t('Remove'),
      cancelButtonText: t('Cancel'),
      className: classNames(styles.modalWrapper),
    });
    if (isConfirmed) {
      onDelete(entity.groupSequence);
    }
  }, [t, entity, onDelete]);

  const correctAnswerElement = useMemo(() => {
    let element = <></>;
    if (questionType === NUMBER_QUESTION.id) {
      element = (
        <NumberField
          columns={3}
          label={t('Correct Answer')}
          max={MAX_INT}
          name="questionCorrectAnswer"
          placeholder={t('Enter the correct numeric answer')}
          precision={2}
          step={0.5}
        />
      );
    } else if (questionType === SHORT_TEXT.id) {
      const TWO_FIFITY = 250;
      element = (
        <TextField
          columns={1}
          label={t('Correct Answer')}
          maxLength={TWO_FIFITY}
          name="questionCorrectAnswer"
          placeholder={t('Enter the correct text answer')}
        />
      );
    } else if (
      [PARAGRAPH_QUESTION.id, ESSAY_QUESTION.id].includes(questionType)
    ) {
      const TEN_THOUSAND = 10000;
      element = (
        <TextAreaField
          columns={1}
          label={t('Correct Answer')}
          maxLength={TEN_THOUSAND}
          name="questionCorrectAnswer"
          placeholder={t('Enter the correct text answer')}
        />
      );
    }
    return element;
  }, [questionType, t]);

  const onOptionTitle = useCallback(
    option => {
      if (!option) {
        return t('None');
      }
      if (isReadOnly) {
        return option.option?.value || t('None');
      }
      const { value } = option;
      return value;
    },
    [t, isReadOnly],
  );

  const maxLimitElement = useMemo(() => {
    let element = <></>;
    if (
      questionType === PARAGRAPH_QUESTION.id ||
      questionType === ESSAY_QUESTION.id
    ) {
      element = (
        <SelectBoxField
          required
          columns={3}
          defaultValue={
            questionType === PARAGRAPH_QUESTION.id ? HUNDRED : FOUR_HUNDERED
          }
          label={t('Maximum Number of Words')}
          name="answerMaxLimit"
          options={
            questionType === PARAGRAPH_QUESTION.id
              ? tenToHundredOneTwentyToTwoHundredTwoHundredFiftyToFourHundred
              : hundredToFiveHundredThousandToFiveThousandTenThousandToFiftyThousand
          }
          returnType={RETURN_TYPE.NUMBER}
          onOptionTitle={onOptionTitle}
        />
      );
    }
    return element;
  }, [questionType, t, onOptionTitle]);

  ///custom submit section
  const submitActions = useMemo(() => {
    const actions = [
      {
        text: t('Save and Close'),
        onClick: onSubmitClose,
        validate: true,
      },
    ];
    return actions;
  }, [onSubmitClose, t]);

  const renderSubmitSection = useCallback(
    ({ isSubmitting, isEditing, isDirty }) => {
      let submitLabel: string | JSX.Element = t('Save');

      if (isSubmitting) {
        submitLabel = <Spinner />;
      }

      return (
        <div
          className={classNames(
            'col-md-12 mb-10 no-padding-left no-padding-right mt-20',
            styles.submitBox,
          )}
        >
          {!isReadOnly && addSubmitOption && (
            <div
              className={classNames(
                'col-lg-3 col-md-3 col-sm-12 col-xs-12 pull-left text-left mb-10',
              )}
            >
              {addSubmitOption(isSubmitting)}
            </div>
          )}
          {!isReadOnly && addNewButtonList && (
            <div
              className={classNames(
                'text-center col-sm-12 col-xs-12',
                isNew ? 'col-lg-5 col-md-5' : 'col-lg-4 col-md-4',
              )}
            >
              {addNewButtonList()}
            </div>
          )}
          <div
            className={classNames(
              isNew && 'col-lg-4 col-md-4 col-sm-12 col-xs-12',
              styles.pr_20,
              styles.footerMobileMargin,
              'pull-right text-right',
            )}
          >
            {isReadOnly ? (
              <RoundedGoBackButton
                id="button-cancel"
                margin="mr-5"
                onClick={onCancel}
              >
                {t('Close')}
              </RoundedGoBackButton>
            ) : (
              <RoundedLinkButton
                id="button-cancel"
                margin="mr-5"
                onClick={onCancel}
              >
                {t('Cancel')}
              </RoundedLinkButton>
            )}

            {!isReadOnly && !isNew && (
              <RoundedDangerButton
                additionClasses="btn btn-danger legitRipple"
                type="button"
                onClick={_onDelete}
              >
                {t('Remove')}
              </RoundedDangerButton>
            )}

            {!isReadOnly && (
              <div className="mt-5 ml-5 display-inline-block">
                <RoundedDropdownButton<IEContentQuestion>
                  actions={submitActions}
                  disabled={isSubmitting || isReadOnly}
                >
                  {isSubmitting ? <Spinner /> : t('Save')}
                </RoundedDropdownButton>
              </div>
            )}
          </div>
        </div>
      );
    },
    [
      onCancel,
      isReadOnly,
      t,
      submitActions,
      isReadOnly,
      _onDelete,
      isNew,
      addNewButtonList,
    ],
  );

  return (
    <EntityForm
      entity={entity as IBasicEntity}
      entityName="QuestionForm"
      hasCreateMessage={false}
      hasUpdateMessage={false}
      hasValidateOnBlur={false}
      isAlwaysEditable={!isReadOnly}
      isNew={isNew}
      isReadOnly={isReadOnly}
      submitSectionRenderer={renderSubmitSection}
      validate={validate}
      validateOnMount={false}
      onSubmit={onSubmit}
    >
      <div
        className={classNames({
          [styles.questionFormSectionContainer]: !isReadOnly,
          'mt-10': !isReadOnly,
        })}
      >
        <div
          className={classNames({
            [styles.questionTitleContainer]: isReadOnly,
          })}
        >
          {isReadOnly && (
            <span className="mb-10">{entity.groupSequence + 1}.</span>
          )}
          <TextEditorField
            hasMoreButton
            clobsPreviewData={clobsPreviewData}
            columns={1}
            config={textEditorConfig}
            customLabel={!isReadOnly && customLabel}
            fileCategory={E_CONTENT_CONTENT_FILE}
            hasRenderedPreview={hasRenderedPreview}
            isReadOnly={isReadOnly}
            label={isReadOnly ? '' : t('Enter question here')}
            maxLength={10000}
            name="questionTitle"
            required={!isReadOnly}
            rows={2}
          />
        </div>
        {!refreshPage && (
          <AttachmentFieldWithPreview
            isDownloadable
            isSubsection
            addButtonTitle={t('Add Image')}
            allowedFileTypes={IMAGES}
            attachmentItemComponent={attachmentItemComponent}
            cardsInRow={4}
            categoryKey={E_CONTENT_CONTENT_FILE}
            columns={1}
            hasErrorMessage={false}
            hasRoutes={false}
            isReadOnly={isReadOnly}
            maxDescriptionLength={10}
            name="questionImage"
            organisationGroupId={organisationGroupId}
            strikeIcon="image"
            strikeTitle={t('Images')}
            tenantId={tenantId}
          />
        )}
      </div>

      <div
        className={classNames({
          [styles.questionFormSectionContainer]: !isReadOnly,
          'pt-10': !isReadOnly,
        })}
      >
        {[
          SINGLE_SELECT.id,
          MULTI_SELECT.id,
          TRUEFALSE.id,
          MULTI_TRUEFALSE.id,
          NUMBER.id,
        ].includes(questionType) && (
          <>
            {!isReadOnly ? (
              <div className="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <AnimatedTitle className="mb-0" placeholder={t('Options')} />
              </div>
            ) : null}
            <QuestionSetOption
              isMultiple={isMultiple}
              refreshPage={refreshPage}
              setCorrectNumber={setCorrectNumber}
            />
          </>
        )}
        {!isReadOnly &&
          [SINGLE_SELECT.id, TRUEFALSE.id, NUMBER.id].includes(
            questionType,
          ) && (
            <CheckboxField
              columns={1}
              disabled={isReadOnly}
              label={t('More than one correct answer')}
              name="questionMultiple"
              onCheck={handleCheck}
              onUncheck={handleUnCheck}
            />
          )}
        {correctNumber > 1 &&
          [SINGLE_SELECT.id, TRUEFALSE.id, NUMBER.id].includes(
            questionType,
          ) && (
            <div className="col-lg-12 mb-10">
              {t(
                'Warning: Multiple correct selected, but only one of them can be selected by user',
              )}
            </div>
          )}
        {correctAnswerElement}
      </div>

      <div
        className={classNames({
          [styles.questionFormSectionContainer]: !isReadOnly,
        })}
      >
        {isReadOnly && !isEmpty(entity.questionLevel) ? (
          <NumberField
            columns={2}
            label={t('Difficulty Level')}
            max={20}
            min={1}
            name="questionLevel"
          />
        ) : null}
        {!isReadOnly ? (
          <NumberField
            columns={2}
            label={t('Difficulty Level')}
            max={20}
            min={1}
            name="questionLevel"
          />
        ) : null}
        {questionType !== NUMBER.id &&
        isReadOnly &&
        !isEmpty(entity.questionPoint) ? (
          <NumberField columns={2} label={t('Points')} name="questionPoint" />
        ) : null}
        {questionType !== NUMBER.id && !isReadOnly ? (
          <NumberField
            columns={2}
            disabled={hasQuestionPoint && !isEmpty(hasQuestionPoint)}
            label={t('Points')}
            name="questionPoint"
          />
        ) : null}
        {maxLimitElement}
        {hasKeyword && isReadOnly && entity.contentKeyword ? (
          <div className="1">
            <TagInputField
              noLabel
              columns={1}
              isReadOnly={isReadOnly}
              label={t('Content Keyword')}
              maxTagLength={maxTagLength}
              name="contentKeyword"
              required={isKeywordRequired}
              suggestions={suggestions}
            />
          </div>
        ) : null}
        {hasKeyword && !isReadOnly ? (
          <div className="2">
            <TagInputField
              noLabel
              columns={1}
              isReadOnly={isReadOnly}
              label={t('Content Keyword')}
              maxTagLength={maxTagLength}
              name="contentKeyword"
              required={isKeywordRequired}
              suggestions={suggestions}
            />
          </div>
        ) : null}
        <div className="mt-20" />
      </div>
    </EntityForm>
  );
};

export default QuestionFormElements;
