import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { unionBy } from 'lodash';
import classNames from 'classnames';

import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CheckboxField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import useT from '../../../../../../../common/components/utils/Translations/useT';

import { useDependsOnFields } from '../../../../../../../common/components/containers/EntityForm';
import SelectBoxField from '../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import {
  zeroToHundred,
  oneToHundred,
  oneToTwenty,
  textCharactersSizes,
  oneToTenTwentyToHundredTwoThousandToFourThousand,
} from '../../../model/EContentAttributesOptions';
import {
  Rich,
  Simple,
  SimpleAndRich,
} from '../../../../../../../model/TextsType';
import { RETURN_TYPE } from '../../../../../../../common/components/controls/base/SelectBox';
import useHandleMinMaxFields from './hooks/useHandleMinMaxFields';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';
import styles from '../../../../../../../common/components/containers/EntityForm/fields/CharactersField.scss';
import mainStyles from './styles.scss';
import useEntityFormContext from '../../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import CheckboxNormal from '../../../../../../../common/components/controls/base/CheckboxNormal';
import MultiSelectBoxField from '../../../../../../../common/components/containers/EntityForm/fields/MultiSelectBoxField';

const EContentResourceXAttributeTexts: React.FC<{
  prefix?: string;
}> = ({ prefix = 'cookedAttributes' }) => {
  const t = useT();
  const { setFieldValue } = useEntityFormContext();

  const { isChecked, textType } = useDependsOnFields<{
    isChecked: boolean;
    textType: number;
  }>({
    isChecked: `${prefix}.text.isChecked`,
    textType: `${prefix}.text.value`,
  });

  const optionsForMaxField = useHandleMinMaxFields({
    minFieldName: `${prefix}.minNumberOfTextBoxes.value`,
    maxFieldName: `${prefix}.maxNumberOfTextBoxes.value`,
    options: unionBy(
      oneToHundred,
      oneToTenTwentyToHundredTwoThousandToFourThousand,
      'value',
    ),
  });

  const optionsForMinFieldUnion = useMemo(
    () => unionBy(zeroToHundred, optionsForMaxField, 'value'),
    [optionsForMaxField],
  );

  const [simpleCheck, setSimpleCheck] = useState(
    textType === Simple.value || textType === SimpleAndRich.value,
  );
  const [richCheck, setRichCheck] = useState(
    textType === Rich.value || textType === SimpleAndRich.value,
  );

  useEffect(() => {
    if (simpleCheck && richCheck) {
      setFieldValue(`${prefix}.text.value`, SimpleAndRich.value);
    } else if (simpleCheck) {
      setFieldValue(`${prefix}.text.value`, Simple.value);
    } else if (richCheck) {
      setFieldValue(`${prefix}.text.value`, Rich.value);
    } else {
      setFieldValue(`${prefix}.text.value`, 0);
    }
  }, [simpleCheck, richCheck, prefix]);

  const onSimpleToggle = useCallback(data => {
    const {
      target: { checked },
    } = data;
    if (!checked) setSimpleCheck(false);
    else setSimpleCheck(true);
  }, []);

  const onRichToggle = useCallback(data => {
    const {
      target: { checked },
    } = data;
    if (!checked) setRichCheck(false);
    else setRichCheck(true);
  }, []);

  return (
    <div className={classNames('pl-10 pr-10', mainStyles.contentWrapper)}>
      <EntityFormFieldSet className={mainStyles.contentTitle}>
        <CheckboxField
          checkboxSpanClassname={classNames(mainStyles.checkboxSpanClassname)}
          columns={1}
          label={t('Texts')}
          name={`${prefix}.text.isChecked`}
        />
      </EntityFormFieldSet>
      {isChecked ? (
        <>
          {textType === Simple.value ||
          textType === Rich.value ||
          textType === SimpleAndRich.value ||
          true ? (
            <div className="pl-20">
              <AnimatedTitle
                isRequired
                className={styles.labelTitle}
                placeholder={t('Number of Text boxes')}
              />
              <EntityFormFieldSet>
                <SelectBoxField
                  isEmptyValueAllowed
                  required
                  columns={3}
                  label={t('From')}
                  name={`${prefix}.minNumberOfTextBoxes.value`}
                  options={optionsForMinFieldUnion}
                  returnType={RETURN_TYPE.NUMBER}
                />
                <SelectBoxField
                  isEmptyValueAllowed
                  required
                  columns={3}
                  label={t('To')}
                  name={`${prefix}.maxNumberOfTextBoxes.value`}
                  options={optionsForMaxField}
                  returnType={RETURN_TYPE.NUMBER}
                />

                <SelectBoxField
                  isEmptyValueAllowed
                  required
                  columns={3}
                  label={t('Height (Lines)')}
                  name={`${prefix}.textHeight.value`}
                  options={oneToTwenty}
                  returnType={RETURN_TYPE.NUMBER}
                />
              </EntityFormFieldSet>
            </div>
          ) : null}
          <div className="pl-20">
            <EntityFormFieldSet rowWrapper={false}>
              <MultiSelectBoxField
                hasSelectAll
                isRequired
                // itemValuePropName={itemValuePropName}
                label={t('Text Box Type')}
                name={`${prefix}.text.value`}
                options={[
                  {
                    id: 1,
                    value: 1,
                    name: 'Simple Text boxes',
                  },
                  {
                    id: 2,
                    value: 2,
                    name: 'Rich Text boxes',
                  },
                ]}
              />
            </EntityFormFieldSet>
          </div>
          <div className="pl-20">
            <EntityFormFieldSet rowWrapper={false}>
              <CheckboxNormal
                formElement
                label={t(Simple.name)}
                name={Simple.name}
                value={simpleCheck}
                onChange={onSimpleToggle}
              />
              <div className="pl-20">
                {(textType === Simple.value ||
                  textType === SimpleAndRich.value) && (
                  <>
                    <EntityFormFieldSet>
                      <SelectBoxField
                        isEmptyValueAllowed
                        required
                        columns={3}
                        label={t('Maximum Characters')}
                        name={`${prefix}.textCharactersNumber.value`}
                        options={textCharactersSizes}
                        returnType={RETURN_TYPE.NUMBER}
                      />
                    </EntityFormFieldSet>
                    <EntityFormFieldSet>
                      <CheckboxField
                        columns={3}
                        label={t('Preview Rendering')}
                        name={`${prefix}.previewRenderingSimpleText.isChecked`}
                      />
                    </EntityFormFieldSet>
                  </>
                )}
              </div>
            </EntityFormFieldSet>
            <EntityFormFieldSet rowWrapper={false}>
              <CheckboxNormal
                formElement
                label={t(Rich.name)}
                name={Rich.name}
                value={richCheck}
                onChange={onRichToggle}
              />
              <EntityFormFieldSet className="pl-20">
                {(textType === Rich.value ||
                  textType === SimpleAndRich.value) && (
                  <CheckboxField
                    columns={3}
                    label={t('Preview Rendering')}
                    name={`${prefix}.previewRenderingRichText.isChecked`}
                  />
                )}
              </EntityFormFieldSet>
            </EntityFormFieldSet>
          </div>
        </>
      ) : null}
    </div>
  );
};

export default EContentResourceXAttributeTexts;
