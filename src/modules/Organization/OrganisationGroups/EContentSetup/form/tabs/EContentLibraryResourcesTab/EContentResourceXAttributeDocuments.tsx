import React from 'react';
import classNames from 'classnames';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CheckboxField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxField';
import useT from '../../../../../../../common/components/utils/Translations/useT';

import { useDependsOnFields } from '../../../../../../../common/components/containers/EntityForm';
import SelectBoxField from '../../../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import EContentResourceXAttributeSubSection from './EContentResourceXAttributeSubSection';
import {
  oneToTenToFifty,
  oneToTenTwentyToHundredTwoThousandToFourThousand,
  zeroToTenTwentyToHundredTwoThousandToFourThousand,
} from '../../../model/EContentAttributesOptions';
import { RETURN_TYPE } from '../../../../../../../common/components/controls/base/SelectBox';
import useHandleMinMaxFields from './hooks/useHandleMinMaxFields';
import AnimatedTitle from '../../../../../../../common/components/controls/base/AnimatedTitle';
import styles from '../../../../../../../common/components/containers/EntityForm/fields/CharactersField.scss';
import mainStyles from './styles.scss';

const EContentResourceXAttributeDocuments: React.FC<{
  prefix?: string;
}> = ({ prefix = 'cookedAttributes' }) => {
  const t = useT();

  const { isChecked } = useDependsOnFields({
    isChecked: `${prefix}.document.isChecked`,
  });

  const optionsForMaxField = useHandleMinMaxFields({
    minFieldName: `${prefix}.minNumberOfDocuments.value`,
    maxFieldName: `${prefix}.maxNumberOfDocuments.value`,
    options: oneToTenTwentyToHundredTwoThousandToFourThousand,
  });

  return (
    <div className={classNames('pl-10 pr-10', mainStyles.contentWrapper)}>
      <EntityFormFieldSet className={mainStyles.contentTitle}>
        <CheckboxField
          checkboxSpanClassname={classNames(mainStyles.checkboxSpanClassname)}
          columns={1}
          label={t('Documents')}
          name={`${prefix}.document.isChecked`}
        />
      </EntityFormFieldSet>
      {isChecked ? (
        <div className="pl-20">
          <AnimatedTitle
            isRequired
            className={styles.labelTitle}
            placeholder={t('Number of Documents')}
          />
          <EntityFormFieldSet>
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('From')}
              name={`${prefix}.minNumberOfDocuments.value`}
              options={zeroToTenTwentyToHundredTwoThousandToFourThousand}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              label={t('To')}
              name={`${prefix}.maxNumberOfDocuments.value`}
              options={optionsForMaxField}
              returnType={RETURN_TYPE.NUMBER}
            />
            <SelectBoxField
              isEmptyValueAllowed
              required
              columns={3}
              infoMessage="MB"
              label={t('Maximum Size of Each Document')}
              name={`${prefix}.maxSizeOfEachDocument.value`}
              options={oneToTenToFifty}
              returnType={RETURN_TYPE.NUMBER}
            />
          </EntityFormFieldSet>
          <EContentResourceXAttributeSubSection
            label={t('Document Caption ')}
            name="documentCaption"
          />
        </div>
      ) : null}
    </div>
  );
};

export default EContentResourceXAttributeDocuments;
