import React, { useCallback, useContext, memo, useState } from 'react';
import { isEmpty } from 'lodash';

import useT from '../../../../../../../common/components/utils/Translations/useT';
import { ProgramGroupFormContext } from '../../../ProgramGroupForm';
import FilterBar from '../../../../../../../common/components/controls/FilterBar/FilterBar';
import CurriculumStructureViewSelector from '../../../../../../../common/components/controls/FilterBar/CurriculumStructureViewSelector';
import ProgramStructureLPTreeSelector from '../../../../../../../common/components/controls/FilterBar/ProgramStructureLPTreeSelector';
import usePaginationRouting from '../../../../../../../common/data/hooks/usePaginationRouting';
import GqlFullCrudTree from '../../../../../../../common/components/dataViews/GqlFullCrudTree';
import nodeSortingOptionsSequence from '../../../../../../../common/utils/nodeSortingOptionsSequence';
import programStructureLPTreeGql from '../../../../../../../common/components/controls/FilterBar/ProgramStructureLPTreeSelector/data/programStructureLPTree.graphql';
import {
  isLearningPlanTask,
  resolveNodeId,
  resolveNodeParentId,
  resolveNodeTitle,
  resolveNodeClassName,
  canRender,
  resolveNodeIconName,
  isLearningPlan,
} from '../../../../../../../common/components/controls/FilterBar/ProgramStructureLPTreeSelector/ProgramStructureLPTreeAdapter';
import ContentPanel from '../../../../../../../common/components/containers/BasicModuleLayout/ContentPanel';
import LPContentTable from './LPContentTable';
import learningPlanTaskGql from '../data/learningPlanTask.graphql';
import SearchField from '../../../../../../../common/components/controls/base/SearchField';
import ActionsAddon, {
  IActionsAddonAction,
} from '../../../../../../../common/components/dataViews/NextTree/addons/ActionsAddon/ActionsAddon';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';

function CurriculumStructureContentView({
  programGroupName,
  defaultFilterValues,
  onDefaultFilterValuesChange,
}) {
  const t = useT();
  const { id: programGroupId } = useContext(ProgramGroupFormContext) || {};
  const { pageNumber, pageSize } = usePaginationRouting();
  const { url } = useRouteMatch();
  const { push } = useHistory();

  const [searchQuery, setSearchQuery] = useState('');

  const onFilterChanged = useCallback((values, name) => {
    if (name === 'view') {
      return { ...values, programStructureLP: [] };
    }
    return { ...values };
  }, []);

  const onFilterChange = useCallback(
    filters => {
      onDefaultFilterValuesChange(filters);
    },
    [onDefaultFilterValuesChange],
  );

  const renderFilter = useCallback(
    ({ value, onChange }) => (
      <div>
        <FilterBar
          values={value}
          onChange={onChange}
          onFilterChanged={onFilterChanged}
        >
          <CurriculumStructureViewSelector name="view" />
          <ProgramStructureLPTreeSelector
            initialSelectAll
            name="programStructureLP"
            programGroupId={programGroupId}
            programGroupName={programGroupName}
            title="Program Structure"
          />
        </FilterBar>
        <SearchField
          placeholder={t('Search')}
          value={searchQuery}
          onChange={setSearchQuery}
        />
      </div>
    ),
    [programGroupId, onFilterChanged, searchQuery, t, programGroupName],
  );

  const programStructureLPTaskIds =
    defaultFilterValues.programStructureLP
      ?.filter(isLearningPlanTask)
      .map(item => item.id) || [];

  const renderNodeAddons = useCallback(
    node => {
      const addons: JSX.Element[] = [];
      const actions: IActionsAddonAction[] = [];

      const handleGoToTasks = () => {
        const link = url.split('/curriculum-structure')[0];
        push(`${link}/learning-plan/${node.id}/edit/learning-plan/tasks`);
      };
      if (isLearningPlan(node)) {
        actions.push({
          id: `go_to_tasks_${node.id}`,
          title: t('Go To Tasks'),
          action: handleGoToTasks,
        });
      }

      if (!isEmpty(actions)) {
        addons.push(
          <ActionsAddon
            key={`actions_${node.id}`}
            actions={actions}
            node={node}
          />,
        );
      }

      return addons;
    },
    [t, push, url],
  );

  return (
    <ContentPanel title={t('Curriculum Structure')}>
      <GqlFullCrudTree
        nodes={{
          task: {
            gql: {
              single: learningPlanTaskGql,
              fetchPolicy: 'cache-first',
            },
            title: 'LPContentTable',
            component: LPContentTable,
            isThisNodeType: isLearningPlanTask,
            programGroupName,
            programStructureLPIds: programStructureLPTaskIds,
            programGroupId,
          },
        }}
        tree={{
          rootName: t(programGroupName),
          key: 'curriculumStructureContents',
          gql: {
            query: programStructureLPTreeGql,
            variables: {
              programGroupId,
              view: defaultFilterValues.view,
              withTaskIds: true,
              programStructureLPTaskIds,
            },
          },
          onlyRootModelsAllowed: false,
          adapter: {
            resolveNodeId,
            resolveNodeParentId,
            resolveNodeTitle,
            resolveNodeClassName,
            canRender,
            renderNodeAddons,
          },
          plugins: {
            nodeSortingOptions: nodeSortingOptionsSequence,
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            resolveNodeIconName,
          },
          filter: {
            value: {
              programGroupId,
              view: defaultFilterValues.view,
              programStructureLP: defaultFilterValues.programStructureLP,
              first: (Number(pageNumber) - 1) * pageSize,
              count: pageSize,
              searchQuery,
            },
            component: renderFilter,
            onChange: onFilterChange,
          },
        }}
      />
    </ContentPanel>
  );
}

export default memo(
  CurriculumStructureContentView,
  (prevProps, nextProps) =>
    prevProps.programGroupName === nextProps.programGroupName &&
    prevProps.defaultFilterValues === nextProps.defaultFilterValues &&
    prevProps.onDefaultFilterValuesChange ===
      nextProps.onDefaultFilterValuesChange,
);
