import React, { useCallback } from 'react';

import FormCheckboxNormal from '../../../controls/FormCheckboxNormal';
import EntityField from '../internal/EntityField';
import { IEntityFieldContentProps } from '../internal/EntityFieldContent';

export interface ICheckboxField
  extends IEntityFieldContentProps<boolean | unknown> {
  disabled?: boolean;
  name: string;
  label?: string;
  required?: boolean;
  className?: string;
  onUncheck?: (checked: boolean) => void;
  onCheck?: (checked: boolean) => void;
  checkboxClassname?: string;
  checkboxContainerClassname?: string;
  checkboxSpanClassname?: string;
}

const CheckboxField: React.FC<ICheckboxField> = ({
  name,
  label = '',
  disabled = false,
  onUncheck,
  onCheck,
  checkboxClassname,
  checkboxContainerClassname,
  checkboxSpanClassname,
  ...rest
}) => {
  const handleOnChange = useCallback(
    ({ onChange }) => data => {
      const {
        target: { checked },
      } = data;
      onChange(checked);
      onUncheck && !checked && onUncheck(checked);
      onCheck && checked && onCheck(checked);
    },
    [onUncheck, onCheck],
  );

  return (
    <EntityField name={name} {...rest}>
      {(isEditing, field) => (
        <FormCheckboxNormal
          label={label}
          onChange={handleOnChange(field.input)}
          {...field}
          checkboxClassname={checkboxClassname}
          checkboxContainerClassname={checkboxContainerClassname}
          checkboxSpanClassname={checkboxSpanClassname}
          isDisabled={disabled || !isEditing}
        />
      )}
    </EntityField>
  );
};

export default CheckboxField;
