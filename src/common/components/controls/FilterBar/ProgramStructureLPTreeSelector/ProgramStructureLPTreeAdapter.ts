import { some, startsWith } from 'lodash';
import { isRootNode } from '../../../dataViews/DynamicTree';

export const Program = 'Program';
export const ProgramStructureSubject = 'ProgramStructureSubject';
export const LearningPlan = 'LearningPlan';
export const LearningPlanCategory = 'LearningPlanCategory';
export const LearningPlanTask = 'LearningPlanTask';

export const isProgram = ({ __typename }) => __typename === Program;
export const isProgramStructureSubject = ({ __typename }) =>
  __typename === ProgramStructureSubject;
export const isLearningPlan = ({ __typename }: any) =>
  __typename === LearningPlan;
export const isLearningPlanCategory = ({ __typename }: any) =>
  __typename === LearningPlanCategory;
export const isLearningPlanTask = ({ __typename }: any) =>
  __typename === LearningPlanTask;

export const resolveNodeTitle = node => node?.name || node?._name;

export const resolveNodeId = node => {
  if (isProgram(node)) {
    return `${Program}-${node.id}`;
  }
  if (isProgramStructureSubject(node)) {
    return `${ProgramStructureSubject}-${node.id}`;
  }
  if (isLearningPlan(node)) {
    return `${LearningPlan}-${node.id}`;
  }
  if (isLearningPlanCategory(node)) {
    return `${LearningPlanCategory}-${node.id}`;
  }
  if (isLearningPlanTask(node)) {
    return `${LearningPlanTask}-${node.id}`;
  }

  return node.id;
};

export const resolveNodeParentId = node => {
  if (isProgram(node)) {
    return null;
  }
  if (isProgramStructureSubject(node)) {
    return `${Program}-${node.programId}`;
  }
  if (isLearningPlan(node)) {
    return `${ProgramStructureSubject}-${node.subjectId}`;
  }
  if (isLearningPlanCategory(node)) {
    if (node.parentId) {
      return `${LearningPlanCategory}-${node.parentId}`;
    }
    return `${LearningPlan}-${node.learningPlanId}`;
  }
  if (isLearningPlanTask(node)) {
    return `${LearningPlanCategory}-${node.learningPlanCategoryId}`;
  }

  return node.parentId || null;
};

export const nodeIsLeaf = node => isLearningPlanTask(node);

export const canRender = ({
  id,
  model,
  leaf,
  nodesMeta: { hasChild },
  state,
}) => {
  const checkLeafExistsRecursively = (nodeId: string): boolean => {
    if (!state.children[nodeId] || !state.children[nodeId].length) {
      return false;
    }

    const leafExists = some(state.children[nodeId], str =>
      startsWith(str, `${LearningPlanTask}-`),
    );

    if (leafExists) {
      return true;
    }

    return some(state.children[nodeId], childId =>
      checkLeafExistsRecursively(childId),
    );
  };

  if ((model.id !== ':ROOT:' || model.id !== null) && !nodeIsLeaf(model)) {
    if (state.children[id]) {
      const leafExists = checkLeafExistsRecursively(id);

      if (!leafExists || leaf) {
        return false;
      }
    } else {
      return false;
    }
  }
  return true;
};

export const resolveNodeClassName = node => {
  if (isRootNode(node)) {
    return 'jstree-node-bold';
  } else if (nodeIsLeaf(node)) {
    return 'jstree-node-italic';
  }
  return '';
};

export const resolveNodeIconName = node => {
  if (isLearningPlan(node)) {
    return {
      type: 'image',
      src: '/images/learning-plan.png',
    };
  }
  if (isProgramStructureSubject(node)) {
    return {
      type: 'image',
      src: '/images/subject.png',
    };
  }
  return '';
};
