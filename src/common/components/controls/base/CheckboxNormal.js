import classnames from 'classnames';
import PropTypes from 'prop-types';
import React from 'react';

import styles from './CheckboxNormal.scss';

const CheckboxNormal = ({
  name,
  value,
  onChange,
  label,
  inputProps,
  disabled,
  className,
  formElement,
  previewIdsTable,
  additionalComponent,
  inline,
  checkboxClassname,
  checkboxContainerClassname,
  checkboxSpanClassname,
}) => (
  <div
    className={classnames('form-group-material', checkboxContainerClassname, {
      'form-group': !!formElement,
      inline,
      'mr-10': inline,
    })}
  >
    <div className={classnames('checkbox', checkboxClassname, { disabled })}>
      <label
        className={classnames(className, {
          'unSelected-radio': !value && disabled,
        })}
      >
        <div
          className={classnames('checker', {
            [styles.checkboxPosition]: previewIdsTable,
            disabled,
          })}
        >
          <span
            className={classnames(checkboxSpanClassname, {
              checked: value,
              'checkbox-chosen-color': !value && disabled,
              'checked-border': value && disabled,
            })}
          >
            <input
              className="styled"
              name={name}
              type="checkbox"
              {...inputProps}
              checked={value}
              disabled={disabled}
              onChange={onChange}
            />
          </span>
          &nbsp;
        </div>
        {additionalComponent && additionalComponent}
        &nbsp;<span className="text">{label}</span>
      </label>
    </div>
  </div>
);

export default CheckboxNormal;

CheckboxNormal.propTypes = {
  name: PropTypes.string,
  value: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  label: PropTypes.string,
  inputProps: PropTypes.object,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  formElement: PropTypes.bool,
  previewIdsTable: PropTypes.bool,
  additionalComponent: PropTypes.elementType,
  inline: PropTypes.bool,
};

CheckboxNormal.defaultProps = {
  name: null,
  value: false,
  label: null,
  inputProps: {},
  disabled: false,
  className: null,
  formElement: false,
  previewIdsTable: false,
  additionalComponent: null,
  inline: false,
};
